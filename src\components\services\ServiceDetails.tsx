'use client'

import { motion } from 'motion/react'
import { ScrollReveal } from '@/components/ui/ScrollReveal'
import { ServiceCard } from './ServiceCard'
import { SERVICES } from '@/utils/constants'
import { Zap, Settings, Droplets, Shield } from 'lucide-react'

const iconMap = {
  Zap,
  Settings,
  Droplets,
  Shield
}

export function ServiceDetails() {
  return (
    <div className="py-16 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <ScrollReveal>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold text-slate-800 mb-6">
              Our <span className="text-blue-600">MEP Services</span>
            </h2>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto">
              Comprehensive MEP engineering solutions with 19+ years of expertise 
              across electrical, mechanical, plumbing, and fire safety systems
            </p>
          </div>
        </ScrollReveal>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {SERVICES.map((service, index) => {
            const IconComponent = iconMap[service.icon as keyof typeof iconMap]
            return (
              <ScrollReveal key={service.id} delay={index * 0.1}>
                <ServiceCard
                  icon={IconComponent}
                  title={service.title}
                  description={service.shortDescription}
                  features={service.features}
                  color={service.color}
                />
              </ScrollReveal>
            )
          })}
        </div>

        {/* Detailed Service Sections */}
        <div className="space-y-16">
          {SERVICES.map((service, index) => {
            const IconComponent = iconMap[service.icon as keyof typeof iconMap]
            return (
              <ScrollReveal key={`detail-${service.id}`} delay={index * 0.1}>
                <motion.div
                  whileHover={{ scale: 1.01 }}
                  className="bg-white rounded-2xl p-8 md:p-12 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    {/* Content */}
                    <div className={index % 2 === 0 ? 'order-1' : 'order-2'}>
                      <div className="flex items-center mb-6">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 ${
                          service.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                          service.color === 'green' ? 'bg-green-100 text-green-600' :
                          service.color === 'orange' ? 'bg-orange-100 text-orange-600' :
                          'bg-red-100 text-red-600'
                        }`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <h3 className="text-2xl md:text-3xl font-bold text-slate-800">
                          {service.title}
                        </h3>
                      </div>

                      <p className="text-lg text-slate-600 leading-relaxed mb-6">
                        {service.fullDescription}
                      </p>

                      <div className="space-y-3">
                        <h4 className="font-semibold text-slate-800 mb-3">Key Services:</h4>
                        {service.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-start">
                            <div className={`w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 ${
                              service.color === 'blue' ? 'bg-blue-600' :
                              service.color === 'green' ? 'bg-green-600' :
                              service.color === 'orange' ? 'bg-orange-600' :
                              'bg-red-600'
                            }`}></div>
                            <span className="text-slate-700">{feature}</span>
                          </div>
                        ))}
                      </div>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className={`mt-6 px-6 py-3 rounded-lg font-semibold text-white transition-colors ${
                          service.color === 'blue' ? 'bg-blue-600 hover:bg-blue-700' :
                          service.color === 'green' ? 'bg-green-600 hover:bg-green-700' :
                          service.color === 'orange' ? 'bg-orange-600 hover:bg-orange-700' :
                          'bg-red-600 hover:bg-red-700'
                        }`}
                      >
                        Learn More About {service.title}
                      </motion.button>
                    </div>

                    {/* Visual/Icon */}
                    <div className={`${index % 2 === 0 ? 'order-2' : 'order-1'} flex justify-center`}>
                      <div className={`w-48 h-48 rounded-full flex items-center justify-center ${
                        service.color === 'blue' ? 'bg-blue-50' :
                        service.color === 'green' ? 'bg-green-50' :
                        service.color === 'orange' ? 'bg-orange-50' :
                        'bg-red-50'
                      }`}>
                        <IconComponent className={`w-24 h-24 ${
                          service.color === 'blue' ? 'text-blue-600' :
                          service.color === 'green' ? 'text-green-600' :
                          service.color === 'orange' ? 'text-orange-600' :
                          'text-red-600'
                        }`} />
                      </div>
                    </div>
                  </div>
                </motion.div>
              </ScrollReveal>
            )
          })}
        </div>

        {/* Call to Action */}
        <ScrollReveal>
          <div className="text-center mt-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-blue-600 to-slate-700 rounded-2xl p-8 md:p-12 text-white"
            >
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Ready to Start Your MEP Project?
              </h3>
              <p className="text-lg text-blue-100 mb-6 max-w-2xl mx-auto">
                Get expert MEP consulting services tailored to your specific needs. 
                Contact us for a consultation today.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-yellow-400 text-slate-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-yellow-300 transition-colors"
              >
                Request Consultation
              </motion.button>
            </motion.div>
          </div>
        </ScrollReveal>
      </div>
    </div>
  )
}
