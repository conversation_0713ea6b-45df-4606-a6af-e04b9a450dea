import { motion, MotionValue } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { useInView } from "framer-motion";

const getSplitElements = (
  text: string,
  type: "chars" | "words" | "lines" | "words, chars"
) => {
  switch (type) {
    case "words":
      return text.split(" ").map((word, i) => ({
        id: `${word}-${i}`,
        content: word + " ",
      }));
    case "lines":
      return text.split("\n").map((line, i) => ({
        id: `line-${i}`,
        content: line,
      }));
    case "words, chars":
      return [...text].map((char, i) => ({
        id: `char-${i}`,
        content: char,
      }));
    case "chars":
    default:
      return [...text].map((char, i) => ({
        id: `char-${i}`,
        content: char,
      }));
  }
};

interface SplitTextProps {
  text: string;
  className?: string;
  delay?: number;
  duration?: number;
  ease?: string;
  splitType?: "chars" | "words" | "lines" | "words, chars";
  from?: Record<string, unknown>;
  to?: Record<string, unknown>;
  threshold?: number;
  textAlign?: string | MotionValue<string>;
  onLetterAnimationComplete?: () => void;
}

const SplitText = ({
  text,
  className = "",
  delay = 100,
  duration = 0.6,
  ease = "easeOut",
  splitType = "chars",
  from = { opacity: 0, y: 40 },
  to = { opacity: 1, y: 0 },
  threshold = 0.1,
  textAlign = "center",
  onLetterAnimationComplete,
}: SplitTextProps) => {
  const ref = useRef<HTMLParagraphElement>(null);
  const isInView = useInView(ref, {
    once: true,
    margin: "0px 0px -20% 0px",
    amount: threshold,
  });
  const [animated, setAnimated] = useState(false);

  useEffect(() => {
    if (isInView && !animated) {
      setAnimated(true);
      onLetterAnimationComplete?.();
    }
  }, [isInView, animated, onLetterAnimationComplete]);

  const splitElements = getSplitElements(text, splitType);

  const container = {
    hidden: {},
    show: {
      transition: {
        staggerChildren: delay / 1000,
      },
    },
  };

  const item = {
    hidden: from,
    show: {
      ...to,
      transition: {
        duration,
        ease,
      },
    },
  };

  return (
    <motion.p
      ref={ref}
      variants={container}
      initial="hidden"
      animate={isInView ? "show" : "hidden"}
      className={`overflow-hidden inline-block whitespace-normal font-space-mono ${className}`}
      style={{
        textAlign,
        wordWrap: "break-word",
      }}
    >
      {splitElements.map(({ id, content }) => (
        <motion.span
          key={id}
          variants={item}
          style={{ display: "inline-block", whiteSpace: "pre" }}
        >
          {content}
        </motion.span>
      ))}
    </motion.p>
  );
};

export default SplitText;
