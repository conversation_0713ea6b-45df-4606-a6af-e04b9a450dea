import React from 'react';
import { Metadata } from 'next';
import ServicesContent from './ServicesContent';

export const metadata: Metadata = {
  title: 'Our Services - JS Consultants | Electrical, HVAC, Fire Safety & More',
  description: 'Comprehensive electrical and MEP engineering services including HVAC, fire safety, plumbing, solar power, and IBMS solutions. Expert engineering consultancy in Chennai.',
  keywords: 'electrical engineering services, HVAC systems, fire safety, plumbing, solar power, IBMS, MEP engineering, Chennai',
  openGraph: {
    title: 'Professional Engineering Services - JS Consultants',
    description: 'Expert electrical and MEP engineering services for residential, commercial, and industrial projects.',
    type: 'website',
    locale: 'en_IN',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Engineering Services - JS Consultants',
    description: 'Comprehensive electrical and MEP engineering solutions for all your project needs.',
  }
};

export default function ServicesPage() {
  return <ServicesContent />;
}
