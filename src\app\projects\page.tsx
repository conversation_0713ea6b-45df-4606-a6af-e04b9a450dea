"use client";

import React, { useState } from 'react';
import { motion } from 'motion/react';
import { useInView } from 'react-intersection-observer';
import Header from '@/components/header/Header';
import Footer from '@/components/footer/Footer';
import ProjectCarousel from '@/components/projects/ProjectCarousel';
import { Filter, ExternalLink, Calendar, MapPin, Building, Users, Award, DollarSign, CheckCircle } from 'lucide-react';
import Image from 'next/image';

const ProjectsPage = () => {
  const [heroRef, heroInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [projectsRef, projectsInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [selectedFilter, setSelectedFilter] = useState('All');

  const filters = ['All', 'Electrical', 'HVAC', 'Fire Safety', 'Plumbing', 'Solar', 'IBMS'];

  const projects = [
    {
      id: 1,
      title: "Corporate Headquarters Complex",
      category: "Electrical",
      status: "Completed",
      location: "Chennai Business District",
      client: "Fortune 500 Technology Company",
      duration: "12 months",
      teamSize: "15 engineers",
      projectValue: "$2.5M",
      description: "Complete MEP engineering for a 25-story corporate headquarters featuring advanced building automation, energy-efficient systems, and state-of-the-art electrical infrastructure with smart building integration.",
      services: ["Power Distribution", "Lighting Design", "Emergency Systems", "Building Automation", "Energy Management"],
      highlights: [
        "LEED Platinum Certification Achieved",
        "Smart building automation integration",
        "99.9% power reliability with redundant systems",
        "40% reduction in energy consumption",
        "Advanced fire safety and security systems"
      ],
      images: ["/images/projects/project-1.jpg", "/images/projects/project-2.jpg", "/images/projects/project-3.jpg"],
      technicalSpecs: {
        "Electrical Load": "2.5 MW",
        "Floors": "25 Floors + 3 Basements",
        "Area": "500,000 sq ft",
        "Certification": "LEED Platinum"
      }
    },
    {
      id: 2,
      title: "Medical Research Facility",
      category: "HVAC",
      status: "Completed",
      location: "Bangalore Medical Hub",
      client: "Leading Healthcare Institution",
      duration: "10 months",
      teamSize: "12 engineers",
      projectValue: "$3.2M",
      description: "Specialized MEP systems for a cutting-edge medical research facility including cleanroom environments, precision climate control, and redundant power systems for critical operations.",
      services: ["HVAC Design", "Cleanroom Systems", "Precision Climate Control", "Redundant Power", "Medical Gas Systems"],
      highlights: [
        "ISO Class 5 Cleanroom Standards",
        "Advanced HEPA filtration systems",
        "Precision temperature and humidity control",
        "24/7 critical system monitoring",
        "Redundant backup systems for reliability"
      ],
      images: ["/images/projects/project-4.jpg", "/images/projects/project-5.jpg", "/images/projects/project-6.jpg"],
      technicalSpecs: {
        "Cleanroom Area": "15,000 sq ft",
        "HVAC Capacity": "500 TR",
        "Filtration": "HEPA + ULPA",
        "Redundancy": "N+1 Systems"
      }
    },
    {
      id: 3,
      title: "Manufacturing Plant Expansion",
      category: "Fire Safety",
      status: "Completed",
      location: "Industrial Corridor, Pune",
      client: "Global Manufacturing Corporation",
      duration: "8 months",
      teamSize: "10 engineers",
      projectValue: "$4.1M",
      description: "Large-scale industrial MEP engineering for manufacturing plant expansion including high-voltage electrical systems, process cooling, specialized ventilation, and comprehensive fire protection systems.",
      services: ["High-Voltage Systems", "Process Cooling", "Industrial Ventilation", "Fire Protection", "Safety Systems"],
      highlights: [
        "High-voltage electrical distribution (11kV)",
        "Advanced fire suppression systems",
        "Process-specific cooling solutions",
        "Compliance with industrial safety standards",
        "Integrated safety monitoring systems"
      ],
      images: ["/images/projects/project-1.jpg", "/images/projects/project-2.jpg", "/images/projects/project-3.jpg"],
      technicalSpecs: {
        "Electrical Capacity": "5 MW",
        "Cooling Load": "1000 TR",
        "Fire Protection": "Deluge + Foam Systems",
        "Safety Rating": "SIL 2 Certified"
      }
    },
    {
      id: 4,
      title: "Luxury Residential Complex",
      category: "Plumbing",
      status: "Completed",
      location: "Premium Location, Mumbai",
      client: "Prestigious Real Estate Developer",
      duration: "14 months",
      teamSize: "18 engineers",
      projectValue: "$1.8M",
      description: "Complete MEP infrastructure for a luxury 300-unit residential complex featuring sustainable water management, premium plumbing fixtures, and smart home integration systems.",
      services: ["Water Supply Systems", "Drainage & STP", "Rainwater Harvesting", "Smart Home Integration", "Premium Fixtures"],
      highlights: [
        "Sustainable water management with 100% recycling",
        "Smart home automation integration",
        "Premium European fixtures throughout",
        "Zero liquid discharge system",
        "Advanced water treatment plant"
      ],
      images: ["/images/projects/project-4.jpg", "/images/projects/project-5.jpg", "/images/projects/project-6.jpg"],
      technicalSpecs: {
        "Units": "300 Luxury Apartments",
        "Water Storage": "2 Million Liters",
        "STP Capacity": "500 KLD",
        "Rainwater Harvesting": "100% Coverage"
      }
    },
    {
      id: 5,
      title: "Solar Power Plant - Commercial Scale",
      category: "Solar",
      status: "Completed",
      location: "Solar Park, Rajasthan",
      client: "Renewable Energy Corporation",
      duration: "6 months",
      teamSize: "14 engineers",
      projectValue: "$5.2M",
      description: "Large-scale commercial solar power installation with advanced grid-tie capabilities, battery storage systems, and comprehensive monitoring infrastructure for optimal performance.",
      services: ["Solar Panel Installation", "Grid Integration", "Battery Storage", "SCADA Systems", "Performance Monitoring"],
      highlights: [
        "2 MW solar capacity installation",
        "Advanced grid-tie with net metering",
        "Intelligent monitoring and control systems",
        "ROI achieved within 3.5 years",
        "99.2% system availability maintained"
      ],
      images: ["/images/projects/project-1.jpg", "/images/projects/project-2.jpg", "/images/projects/project-3.jpg"],
      technicalSpecs: {
        "Solar Capacity": "2 MW",
        "Panel Type": "Monocrystalline",
        "Battery Storage": "500 kWh",
        "Grid Connection": "11kV Substation"
      }
    },
    {
      id: 6,
      title: "Intelligent Campus Management",
      category: "IBMS",
      status: "Completed",
      location: "IT Corridor, Hyderabad",
      client: "Leading Technology Campus",
      duration: "8 months",
      teamSize: "12 engineers",
      projectValue: "$2.8M",
      description: "Implementation of comprehensive intelligent building management system for a modern technology campus with integrated automation, energy management, and predictive maintenance capabilities.",
      services: ["Building Automation", "Energy Management", "Security Integration", "Environmental Monitoring", "Predictive Analytics"],
      highlights: [
        "Centralized control of all building systems",
        "Real-time monitoring and analytics",
        "Predictive maintenance algorithms",
        "45% reduction in energy consumption",
        "Integrated security and access control"
      ],
      images: ["/images/projects/project-4.jpg", "/images/projects/project-5.jpg", "/images/projects/project-6.jpg"],
      technicalSpecs: {
        "Buildings": "5 Connected Buildings",
        "Control Points": "10,000+ IoT Sensors",
        "Energy Savings": "45% Reduction",
        "Integration": "Full System Integration"
      }
    }
  ];

  const filteredProjects = selectedFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category === selectedFilter);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section ref={heroRef} className="relative py-20 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/projects/project-1.jpg"
            alt="Professional MEP Engineering Projects Portfolio"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={heroInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 bg-blue-600/20 text-blue-300 rounded-full text-sm font-medium backdrop-blur-sm border border-blue-400/30 mb-4"
            >
              <Award className="w-4 h-4 mr-2" />
              Award-Winning Project Portfolio
            </motion.div>

            <h1 className="text-4xl lg:text-6xl font-bold text-white">
              Our <span className="text-blue-400">Engineering Projects</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Explore our portfolio of successful MEP engineering projects across diverse industries, showcasing innovation, technical excellence, and sustainable solutions.
            </p>

            {/* Project Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-8 max-w-4xl mx-auto"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white">500+</div>
                <div className="text-sm text-gray-300">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">$50M+</div>
                <div className="text-sm text-gray-300">Project Value</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">98%</div>
                <div className="text-sm text-gray-300">Client Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">15+</div>
                <div className="text-sm text-gray-300">Years Experience</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap items-center justify-center gap-4">
            <div className="flex items-center space-x-2 text-gray-600">
              <Filter className="w-5 h-5" />
              <span className="font-medium">Filter by:</span>
            </div>
            {filters.map((filter) => (
              <motion.button
                key={filter}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedFilter(filter)}
                className={`px-4 py-2 rounded-full font-medium transition-colors ${
                  selectedFilter === filter
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter}
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section ref={projectsRef} className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 50 }}
                animate={projectsInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                {/* Project Images Carousel */}
                <div className="relative h-64 bg-gray-200">
                  <ProjectCarousel images={project.images} />
                  <div className="absolute top-4 left-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      project.status === 'Completed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {project.status}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4 flex flex-col space-y-2">
                    <span className="px-3 py-1 bg-white/90 text-gray-800 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                    <span className="px-3 py-1 bg-green-600/90 text-white rounded-full text-sm font-medium">
                      {project.projectValue}
                    </span>
                  </div>
                </div>

                {/* Project Details */}
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{project.title}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{project.description}</p>

                  {/* Project Info */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="w-4 h-4 text-blue-600" />
                      <span>{project.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Building className="w-4 h-4 text-green-600" />
                      <span>{project.client}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Calendar className="w-4 h-4 text-purple-600" />
                      <span>{project.duration}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="w-4 h-4 text-orange-600" />
                      <span>{project.teamSize}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600 col-span-2">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <span className="font-semibold">Project Value: {project.projectValue}</span>
                    </div>
                  </div>

                  {/* Services */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Services Provided</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.services.map((service, idx) => (
                        <span key={idx} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Technical Specifications */}
                  {project.technicalSpecs && (
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Technical Specifications</h4>
                      <div className="grid grid-cols-2 gap-3">
                        {Object.entries(project.technicalSpecs).map(([key, value], idx) => (
                          <div key={idx} className="bg-gray-50 rounded-lg p-3">
                            <div className="text-xs text-gray-500 uppercase tracking-wide">{key}</div>
                            <div className="text-sm font-semibold text-gray-900">{value}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Key Highlights */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Achievements</h4>
                    <ul className="space-y-2">
                      {project.highlights.map((highlight, idx) => (
                        <li key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                          <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                          <span>{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View Project Details
                    <ExternalLink className="ml-2 w-4 h-4" />
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 to-purple-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={projectsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-white">
              Have a Project in Mind?
            </h2>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Let's discuss how we can bring your vision to life with our expertise and proven track record.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors"
            >
              Start Your Project
              <ExternalLink className="ml-2 w-5 h-5" />
            </motion.button>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ProjectsPage;
