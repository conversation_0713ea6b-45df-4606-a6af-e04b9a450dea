'use client'

import { motion } from 'motion/react'
import { LucideIcon, ArrowRight } from 'lucide-react'

interface ServiceCardProps {
  icon: LucideIcon
  title: string
  description: string
  features: string[]
  color: string
  delay?: number
}

const getColorClasses = (color: string) => {
  const colorMap = {
    blue: {
      bg: "bg-blue-50",
      icon: "bg-blue-100 text-blue-600",
      accent: "text-blue-600",
      button: "bg-blue-600 hover:bg-blue-700"
    },
    green: {
      bg: "bg-green-50",
      icon: "bg-green-100 text-green-600",
      accent: "text-green-600",
      button: "bg-green-600 hover:bg-green-700"
    },
    orange: {
      bg: "bg-orange-50",
      icon: "bg-orange-100 text-orange-600",
      accent: "text-orange-600",
      button: "bg-orange-600 hover:bg-orange-700"
    },
    red: {
      bg: "bg-red-50",
      icon: "bg-red-100 text-red-600",
      accent: "text-red-600",
      button: "bg-red-600 hover:bg-red-700"
    }
  }
  return colorMap[color as keyof typeof colorMap] || colorMap.blue
}

export function ServiceCard({ 
  icon: Icon, 
  title, 
  description, 
  features, 
  color, 
  delay = 0 
}: ServiceCardProps) {
  const colors = getColorClasses(color)

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
      whileHover={{ y: -10, scale: 1.02 }}
      className={`${colors.bg} rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50`}
    >
      {/* Icon */}
      <div className={`w-16 h-16 ${colors.icon} rounded-xl flex items-center justify-center mb-6`}>
        <Icon className="w-8 h-8" />
      </div>

      {/* Title */}
      <h3 className="text-2xl font-bold text-slate-800 mb-4">
        {title}
      </h3>

      {/* Description */}
      <p className="text-slate-600 leading-relaxed mb-6">
        {description}
      </p>

      {/* Features */}
      <div className="space-y-3 mb-8">
        {features.map((feature, index) => (
          <div key={index} className="flex items-start">
            <div className={`w-2 h-2 ${colors.icon.split(' ')[0]} rounded-full mt-2 mr-3 flex-shrink-0`}></div>
            <span className="text-slate-700 text-sm">{feature}</span>
          </div>
        ))}
      </div>

      {/* CTA Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={`w-full ${colors.button} text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center group`}
      >
        Learn More
        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
      </motion.button>
    </motion.div>
  )
}
