'use client'

import { motion } from 'motion/react'
import { ScrollReveal } from '@/components/ui/ScrollReveal'
import { Users, Heart, Lightbulb, Award, Coffee, Zap } from 'lucide-react'

const cultureValues = [
  {
    icon: Users,
    title: "Amazing Team Feel",
    description: "We all pull together and help each other when needed. Our collaborative environment fosters growth and innovation.",
    color: "blue"
  },
  {
    icon: Heart,
    title: "Work-Life Balance",
    description: "We believe in maintaining a healthy balance between professional excellence and personal well-being.",
    color: "red"
  },
  {
    icon: Lightbulb,
    title: "Innovation & Learning",
    description: "Continuous learning and innovative thinking are at the core of our engineering solutions.",
    color: "yellow"
  },
  {
    icon: Award,
    title: "Excellence Recognition",
    description: "We celebrate achievements and recognize outstanding contributions to our projects and team.",
    color: "green"
  },
  {
    icon: Coffee,
    title: "Inclusive Environment",
    description: "A welcoming workplace where diverse perspectives are valued and everyone can thrive.",
    color: "purple"
  },
  {
    icon: Zap,
    title: "Growth Opportunities",
    description: "Clear career progression paths with opportunities to work on challenging and impactful projects.",
    color: "orange"
  }
]

const getColorClasses = (color: string) => {
  const colorMap = {
    blue: "bg-blue-100 text-blue-600",
    red: "bg-red-100 text-red-600",
    yellow: "bg-yellow-100 text-yellow-600",
    green: "bg-green-100 text-green-600",
    purple: "bg-purple-100 text-purple-600",
    orange: "bg-orange-100 text-orange-600"
  }
  return colorMap[color as keyof typeof colorMap] || "bg-blue-100 text-blue-600"
}

export function TeamCulture() {
  return (
    <section className="py-16 bg-slate-50">
      <div className="container mx-auto px-4">
        <ScrollReveal>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
              Why Join <span className="text-blue-600">JS Consultants?</span>
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Experience a culture of excellence, collaboration, and growth in MEP engineering
            </p>
          </div>
        </ScrollReveal>

        {/* Culture Values Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {cultureValues.map((value, index) => {
            const IconComponent = value.icon
            return (
              <ScrollReveal key={index} delay={index * 0.1}>
                <motion.div
                  whileHover={{ y: -5, scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className={`w-12 h-12 rounded-lg ${getColorClasses(value.color)} flex items-center justify-center mb-4`}>
                    <IconComponent className="w-6 h-6" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-800 mb-3">
                    {value.title}
                  </h3>
                  <p className="text-slate-600 leading-relaxed">
                    {value.description}
                  </p>
                </motion.div>
              </ScrollReveal>
            )
          })}
        </div>

        {/* Team Testimonial */}
        <ScrollReveal>
          <div className="bg-gradient-to-r from-blue-600 to-slate-700 rounded-2xl p-8 md:p-12 text-center text-white">
            <div className="max-w-3xl mx-auto">
              <div className="text-6xl mb-6">"</div>
              <blockquote className="text-xl md:text-2xl font-medium mb-6 italic">
                Amazing team feel – we all pull together, and help each other when needed. 
                The collaborative environment and shared commitment to excellence makes 
                JS Consultants a great place to build your engineering career.
              </blockquote>
              <div className="text-blue-200">
                <p className="font-semibold">JS Consultants Team Member</p>
                <p className="text-sm">Engineering Professional</p>
              </div>
            </div>
          </div>
        </ScrollReveal>

        {/* Benefits Section */}
        <div className="mt-16">
          <ScrollReveal>
            <div className="text-center mb-8">
              <h3 className="text-2xl md:text-3xl font-bold text-slate-800 mb-4">
                Employee Benefits
              </h3>
              <p className="text-lg text-slate-600">
                Comprehensive benefits package for our team members
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              "Competitive Salary",
              "Health Insurance",
              "Professional Development",
              "Flexible Work Hours",
              "Performance Bonuses",
              "Training Programs",
              "Career Advancement",
              "Team Building Events"
            ].map((benefit, index) => (
              <ScrollReveal key={index} delay={index * 0.05}>
                <div className="bg-white rounded-lg p-4 shadow-md text-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  </div>
                  <p className="font-medium text-slate-800">{benefit}</p>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
