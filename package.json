{"name": "jsconsultants", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "keen-slider": "^6.8.6", "lucide-react": "^0.523.0", "motion": "^12.19.1", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}