"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, useState } from "react";
import Image from "next/image";

interface ClientLogo {
  id: string;
  name: string;
  src: string;
  alt: string;
}

interface ClientCarouselProps {
  clients?: ClientLogo[];
  className?: string;
}

const ClientsSection: React.FC<ClientCarouselProps> = ({
  clients = sampleClients,
  className = "",
}) => {
   const containerRef = useRef<HTMLDivElement>(null);
  


  // Create multiple copies for seamless infinite loop
  const duplicatedClients = [...clients, ...clients, ...clients, ...clients];

  return (
    <div 
      ref={containerRef}
      className={`w-full bg-white py-16 overflow-hidden ${className}`}
    >
      <div className="space-y-8">
        {/* First Row - Left to Right */}
        <div className="relative">
          <motion.div
            className="flex gap-8 w-max"
            animate={{
              x: [0, -25 + '%']
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration:  20, 
                ease: "linear",
              },
            }}
          
          >
            {duplicatedClients.map((client, index) => (
              <div
                key={`${client.id}-${index}`}
                className="flex-shrink-0 w-32 h-16 relative grayscale hover:grayscale-0 transition-all duration-300"
              >
                <Image
                  src={client.src}
                  alt={client.alt}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 128px, 128px"
                />
              </div>
            ))}
          </motion.div>
        </div>

        {/* Second Row - Right to Left */}
        <div className="relative">
          <motion.div
            className="flex gap-8 w-max"
            animate={{
              x: [-25 + '%', 0]
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: 20, // Slower on hover
                ease: "linear",
              },
            }}
        
          >
            {duplicatedClients.map((client, index) => (
              <div
                key={`${client.id}-reverse-${index}`}
                className="flex-shrink-0 w-32 h-16 relative grayscale hover:grayscale-0 transition-all duration-300"
              >
                <Image
                  src={client.src}
                  alt={client.alt}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 128px, 128px"
                />
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ClientsSection;

export const sampleClients: ClientLogo[] = [
  {
    id: "1",
    name: "Company A",
    src: "/images/placeholder.jpg",
    alt: "Company A Logo",
  },
  {
    id: "2",
    name: "Company B",
    src: "/images/placeholder.jpg",
    alt: "Company B Logo",
  },
  {
    id: "3",
    name: "Company C",
    src: "/images/placeholder.jpg",
    alt: "Company C Logo",
  },
  {
    id: "4",
    name: "Company D",
    src: "/images/placeholder.jpg",
    alt: "Company D Logo",
  },
  {
    id: "5",
    name: "Company E",
    src: "/images/placeholder.jpg",
    alt: "Company E Logo",
  },
  {
    id: "6",
    name: "Company F",
    src: "/images/placeholder.jpg",
    alt: "Company F Logo",
  },
];
