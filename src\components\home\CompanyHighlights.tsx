"use client";

import React from "react";
import { motion } from "motion/react";
import { Shield, Award, Users, Zap, CheckCircle, Star } from "lucide-react";
import Image from "next/image";

const highlights = [
  {
    icon: Shield,
    title: "Licensed & Trusted Experts",
    description:
      "Our senior consultants and engineers bring decades of licensed expertise, backed by deep technical knowledge and field-tested reliability.",
    features: [
      "18+ Years Industry Experience",
      // "B.E. Graduates from Top Universities",
      // "CEIG-Approved Electrical Designs",
      "Trusted by Major Commercial & Institutional Clients",
    ],
  },
  // {
  //   icon: Award,
  //   title: "Recognized Project Excellence",
  //   description:
  //     "From smart IT campuses to luxury villas, our MEP designs stand out for quality, innovation, and timely execution.",
  //   features: [
  //     "Premium Electrical Switchgear Designs",
  //     "Award-Winning Sustainable Projects",
  //     "LEED & NBC-Compliant Work",
  //     "Proven Client Satisfaction",
  //   ],
  // },
  {
    icon: Users,
    title: "Multi-Disciplinary Team",
    description:
      "Our strength lies in collaboration — combining electrical, HVAC, fire safety, plumbing, and solar experts under one roof.",
    features: [
      "Skilled Electrical Engineers & Draftsmen",
      // "MEP Experts Across Disciplines",
      // "Specialists in Audit & Optimization",
      "Collaborative Design Execution",
    ],
  },
  {
    icon: Zap,
    title: "Driven by Innovation",
    description:
      "From solar integration to smart energy audits, we continuously evolve to deliver future-ready and efficient MEP solutions.",
    features: [
      // "Solar PV Design & Net Metering",
      // "Energy-Efficient HVAC & Lighting",
      "Smart Building Integration",
      "Sustainable & Green Building Goals",
    ],
  },
];

const CompanyHighlights = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-white to-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-sm font-medium mb-4"
          >
            <Star className="w-4 h-4 mr-2" />
            Why Choose JS Consultants
          </motion.div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Engineering Excellence You Can Trust
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            With over 15 years of experience and a commitment to innovation, we
            deliver MEP engineering solutions that exceed expectations and stand
            the test of time.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Column - Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="/images/about/office-interior.jpg"
                alt="JS Consultants Engineering Team"
                width={600}
                height={400}
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/40 to-transparent"></div>
            </div>
          </motion.div>

          {/* Right Column - Highlights */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {highlights.map((highlight, index) => (
              <motion.div
                key={highlight.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex space-x-4"
              >
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <highlight.icon className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {highlight.title}
                  </h3>
                  <p className="text-gray-600 mb-3">{highlight.description}</p>
                  <div className="grid grid-cols-2 gap-2">
                    {highlight.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-center text-sm text-gray-500"
                      >
                        <CheckCircle className="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CompanyHighlights;
