# **Product Requirements Document (PRD)**
## **JS Consultants Corporate Website**

---

## **1. Project Overview**

**Objective**: Build a professional corporate website for **JS Consultants**, an electrical and MEP engineering consultancy, showcasing their services, leadership, projects, and contact details.

**Current Status**: 
- ✅ Next.js 15.3.4 project initialized with TypeScript
- ✅ Framer Motion (via `motion` package) integrated
- ✅ Tailwind CSS v4 configured with custom design system
- ✅ Sophisticated intro animation sequence completed
- ✅ Professional header with navigation and services dropdown
- ✅ Custom logo component and branding elements
- ✅ Advanced UI components (DecryptedText, ShinyText animations)
- ⚠️ Hero section placeholder (needs content)
- ❌ Missing: About, Services, Projects, Contact pages
- ❌ Missing: Carousel implementation
- ❌ Missing: Footer component

---

## **2. <PERSON> Stack (Current Implementation)**

✅ **Frontend Framework**: Next.js 15.3.4 with App Router
✅ **Styling**: Tailwind CSS v4 with custom design tokens
✅ **Animations**: Motion (Framer Motion) v12.19.1
✅ **UI Components**: Radix UI primitives (@radix-ui/react-*)
✅ **Icons**: Lucide React
✅ **Typography**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Mono fonts
✅ **Development**: TypeScript, ESLint

**Still Needed**:
- Carousel library (recommend: `keen-slider` or `embla-carousel-react`)
- Form handling (recommend: `react-hook-form` + `zod`)
- Image optimization utilities

---

## **3. Current Architecture Analysis**

### **3.1. Existing Components**
- **Intro Component**: Sophisticated loading animation with logo reveal, split text, and progress bar
- **Header Component**: Professional navigation with services dropdown, mobile menu
- **Hero Component**: Placeholder (needs implementation)
- **UI Components**: DecryptedText, ShinyText, navigation-menu, sheet
- **Logo**: Custom SVG JsConsultants component

### **3.2. Design System**
- **Colors**: Professional blue (#049BCF), neutral grays, clean whites
- **Typography**: Space Mono for branding, Geist for body text
- **Animations**: Sophisticated motion sequences, hover effects, loading states
- **Layout**: Responsive grid system, sticky navigation

---

## **4. Implementation Roadmap**

### **Phase 1: Core Pages (Priority 1)**

#### **4.1. Hero Section Enhancement**
- Replace placeholder with compelling hero content
- Add animated headline: "Transforming Your Dream Projects Into Reality"
- Include background image/video or subtle engineering graphics
- Call-to-action buttons with motion effects
- Service highlights carousel

#### **4.2. About Us Page**
- Company introduction and mission statement
- Leadership team section with animated profile cards
- Mr. Senthil Nathan and Mr. Sankaranarayanan S profiles
- Company values and expertise highlights
- Downloadable company brochure link

#### **4.3. Services Page**
- Detailed service sections:
  - Electrical Engineering
  - HVAC Systems
  - Fire Fighting Systems
  - Plumbing
  - Solar Power Solutions
  - IBMS (Intelligent Building Management Systems)
- Interactive service cards with hover animations
- Technical specifications and capabilities
- Smooth scroll navigation between services

#### **4.4. Projects Page**
- Project showcase with image carousel
- Filter by project type/status
- Project details with descriptions
- Link to external project tracking (Notion integration)
- Before/after project galleries

#### **4.5. Contact Page**
- Contact form with validation
- Company information display
- Google Maps integration
- GST and registration details
- Multiple contact methods

### **Phase 2: Enhanced Features (Priority 2)**

#### **4.6. Advanced Animations**
- Page transition animations
- Scroll-triggered reveals
- Interactive service demonstrations
- Loading states for all components

#### **4.7. Content Management**
- Dynamic project updates
- News/updates section
- Testimonials carousel
- Case studies

---

## **5. Technical Requirements**

### **5.1. Performance**
- Core Web Vitals optimization
- Image optimization with Next.js Image
- Lazy loading for non-critical content
- Bundle size optimization

### **5.2. SEO & Accessibility**
- Meta tags and structured data
- ARIA labels and semantic HTML
- Alt text for all images
- Keyboard navigation support

### **5.3. Responsive Design**
- Mobile-first approach
- Tablet and desktop optimizations
- Touch-friendly interactions
- Cross-browser compatibility

---

## **6. Content Requirements**

### **6.1. Text Content**
- Company introduction and mission
- Service descriptions and technical specs
- Leadership bios and photos
- Project descriptions and images
- Contact information and forms

### **6.2. Media Assets**
- High-quality project photos
- Leadership professional headshots
- Company logo variations
- Service icons and illustrations
- Background images/videos

---

## **7. Integration Points**

### **7.1. External Services**
- Google Maps for location
- Email service for contact forms
- Analytics tracking (Google Analytics)
- Social media links (if applicable)

### **7.2. Future Integrations**
- CMS integration (Sanity/Contentful)
- Project management system
- Client portal access
- Document management

---

## **8. Success Metrics**

### **8.1. Technical Metrics**
- Page load speed < 3 seconds
- Lighthouse score > 90
- Mobile responsiveness score > 95
- Zero accessibility violations

### **8.2. Business Metrics**
- Contact form submissions
- Service page engagement
- Project portfolio views
- Professional inquiry quality

---

## **9. Deployment & Maintenance**

### **9.1. Deployment**
- Vercel hosting (recommended for Next.js)
- Custom domain configuration
- SSL certificate setup
- CDN optimization

### **9.2. Maintenance**
- Regular content updates
- Security patches
- Performance monitoring
- Backup strategies

---

## **10. Timeline Estimate**

**Phase 1 (Core Implementation)**: 2-3 weeks
- Hero section: 2-3 days
- About page: 3-4 days
- Services page: 4-5 days
- Projects page: 3-4 days
- Contact page: 2-3 days

**Phase 2 (Enhancement)**: 1-2 weeks
- Advanced animations: 3-4 days
- Performance optimization: 2-3 days
- Testing and refinement: 2-3 days

**Total Estimated Timeline**: 3-5 weeks

---

## **11. Next Immediate Steps**

1. **Install carousel dependency**: `npm install keen-slider`
2. **Implement Hero section** with animated content
3. **Create About page** with leadership profiles
4. **Build Services page** with interactive elements
5. **Develop Projects showcase** with carousel
6. **Complete Contact page** with form functionality
7. **Add Footer component** with company information
8. **Optimize and test** across devices

---

*This PRD serves as the blueprint for completing the JS Consultants website, building upon the excellent foundation already established with sophisticated animations and professional design.*
