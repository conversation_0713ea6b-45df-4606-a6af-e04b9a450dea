
import React from "react";



type Props = {
  className?: string;
  width?: string;
  height?: string;
  strokeWidth?: string;
};

const lineSVG = (props: Props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 532 20"
      fill="none"
      {...props}
 
      className={props.className}
      width={props.width || 532}
      height={props.height || 20}
    >
      <path
        d="M1.60744 5.2576C1.41189 5.23306 1.21348 5.28461 1.05548 5.40351C0.897485 5.52239 0.792908 5.69845 0.764684 5.89343C0.736461 6.08841 0.786825 6.28689 0.904626 6.44569C1.02242 6.60452 1.19807 6.71021 1.39256 6.74213C1.39256 6.74213 1.39256 6.74213 1.39256 6.74213C10.0285 8.15978 18.8971 9.45329 27.6577 10.6134C62.8354 15.2499 98.2533 18.0814 133.724 19.1833C184.762 20.732 235.843 19.1914 286.578 13.3049C359.033 5.28559 432.393 -0.243493 504.967 6.89993C513.721 8.02253 522.614 9.23809 530.724 12.1977C530.909 12.2695 531.115 12.2659 531.297 12.1868C531.479 12.1077 531.622 11.9598 531.695 11.7755C531.768 11.5911 531.765 11.3852 531.686 11.203C531.608 11.0207 531.46 10.8771 531.276 10.803C531.276 10.803 531.276 10.803 531.276 10.803C522.897 7.63115 514.011 6.33319 505.197 5.09227C432.348 -2.9373 358.843 2.00942 286.167 9.69574C235.614 15.315 184.72 16.7289 133.847 15.1852C98.4913 14.0866 63.1627 11.7976 27.9368 8.2297C19.1641 7.33664 10.2748 6.34299 1.60744 5.2576Z"
        fill="#1D5DFF"
        strokeWidth={props.strokeWidth || 4}
        strokeLinecap="round"
       
      />
    </svg>
  );
};

export default lineSVG;
