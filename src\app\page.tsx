"use client";
import { useState, useEffect } from "react";
import Header from "@/components/header/Header";
import <PERSON> from "@/components/hero/Hero";
import Intro from "@/components/intro/Intro";
import ServicesOverview from "@/components/home/<USER>";
import FeaturedProjects from "@/components/home/<USER>";
import CompanyHighlights from "@/components/home/<USER>";
import TestimonialsSection from "@/components/home/<USER>";
import Footer from "@/components/footer/Footer";
import ClientsSection from "@/components/home/<USER>";
import CallToActionSection from "@/components/home/<USER>";

export default function Home() {
  const [showIntro, setShowIntro] = useState(true);
  const [isFirstVisit, setIsFirstVisit] = useState(true);

  useEffect(() => {
    // Check if user has visited before in this session
    const hasVisited = sessionStorage.getItem("hasVisitedHome");

    if (hasVisited) {
      setShowIntro(false);
      setIsFirstVisit(false);
    } else {
      // Mark as visited for this session
      sessionStorage.setItem("hasVisitedHome", "true");
    }
  }, []);

  // Handle intro completion
  const handleIntroComplete = () => {
    setShowIntro(false);
  };

  return (
    <main className="relative">
      {isFirstVisit && showIntro && <Intro onComplete={handleIntroComplete} />}
      <div className="relative z-0">
        <Header />
        <Hero />
        <ServicesOverview />
        <FeaturedProjects />
        <CompanyHighlights />
        <TestimonialsSection />
        <ClientsSection className="py-16" />
        <CallToActionSection />

        <Footer />
      </div>
    </main>
  );
}
