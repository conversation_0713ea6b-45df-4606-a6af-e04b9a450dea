'use client'

import { motion } from 'motion/react'
import { Star, Quote, Building, MapPin, Calendar, Award } from 'lucide-react'
import Image from 'next/image'

import { TestimonialCard } from '@/components/testimonials/TestimonialCard'
import { TestimonialCarousel } from '@/components/testimonials/TestimonialCarousel';
import ScrollReveal from '@/components/ui/ScrollReveal';
import Header from '@/components/header/Header';
import Footer from '@/components/footer/Footer';


const testimonials = [
  {
    id: 1,
    company: "Gnanam School of Business and Hotel Gnanam",
    location: "Thanjavur, Tamil Nadu",
    contactPerson: "Dr. K. <PERSON>",
    designation: "Chairman & Managing Director",
    testimonial: "We, the Gnanam School of Business and Hotel Gnanam, Thanjavur have a long association of more than 14+ years with JS Consultants. The organization stands totally committed to delivering quality output, seamless service and exceptional value to their customers. Their expertise in educational facility MEP systems is unmatched.",
    duration: "14+ years association",
    category: "Educational & Hospitality",
    projectType: "Educational Campus MEP Systems",
    projectValue: "$800K",
    rating: 5,
    logo: "/images/testimonials/client-1.jpg",
    completedYear: "2023",
    services: ["Electrical Systems", "HVAC Design", "Fire Safety", "Building Automation"]
  },
  {
    id: 2,
    company: "KC Textiles Private Limited",
    location: "Thiruchendur, Tamil Nadu",
    contactPerson: "Mr. K. Chandrasekaran",
    designation: "Managing Director",
    testimonial: "Initially, we were concerned about the investment, but the electrical systems designed by JS Consultants are incredibly comfortable and energy-efficient. The ROI has been exceptional, and we are enjoying every moment of the superior service provided by the JS Team. Their industrial expertise is remarkable.",
    duration: "8+ years partnership",
    category: "Textile Manufacturing",
    projectType: "Industrial Electrical & Automation",
    projectValue: "$1.2M",
    rating: 5,
    logo: "/images/testimonials/client-2.jpg",
    completedYear: "2022",
    services: ["Industrial Electrical", "Power Distribution", "Energy Management", "Process Automation"]
  },
  {
    id: 3,
    company: "Pothys Textile & Retail",
    location: "Multiple Locations, South India",
    contactPerson: "Mr. S. Sundaram",
    designation: "Executive Director",
    testimonial: "JS Consultants, headed by Mr. Senthil Nathan, have been our trusted electrical consultants for several years. They are energetic and enthusiastic in their approach, consistently bringing innovative ideas and cutting-edge solutions to our retail spaces. Their work has significantly enhanced our customer experience.",
    duration: "6+ years partnership",
    category: "Retail & Commercial",
    projectType: "Multi-location Retail MEP",
    projectValue: "$2.1M",
    rating: 5,
    logo: "/images/testimonials/client-1.jpg",
    completedYear: "2023",
    services: ["Retail Lighting", "HVAC Systems", "Electrical Distribution", "Smart Controls"]
  },
  {
    id: 4,
    company: "Heritage Textiles Corporation",
    location: "Coimbatore, Tamil Nadu",
    contactPerson: "Mr. R. Krishnamurthy",
    designation: "Third Generation Owner",
    testimonial: "With 60 years in the textile industry as a third-generation business, we have worked with many consultants. JS Consultants, particularly Mr. Senthil Nathan, paid exceptional attention to detail and offered creative, practical suggestions. Their lighting design expertise has transformed our manufacturing facility's efficiency and worker comfort.",
    duration: "5+ years collaboration",
    category: "Textile Manufacturing",
    projectType: "Manufacturing Facility Upgrade",
    projectValue: "$950K",
    rating: 5,
    logo: "/images/testimonials/client-2.jpg",
    completedYear: "2022",
    services: ["Industrial Lighting", "Power Systems", "Ventilation", "Safety Systems"]
  },
  {
    id: 5,
    company: "TechCorp Solutions",
    location: "Chennai, Tamil Nadu",
    contactPerson: "Ms. Priya Sharma",
    designation: "Facilities Manager",
    testimonial: "JS Consultants delivered an outstanding smart building solution for our corporate headquarters. Their expertise in building automation and energy management has resulted in 35% energy savings and significantly improved our operational efficiency. The team's professionalism and technical competence are exemplary.",
    duration: "3+ years partnership",
    category: "Corporate & Technology",
    projectType: "Smart Building Implementation",
    projectValue: "$1.8M",
    rating: 5,
    logo: "/images/testimonials/client-1.jpg",
    completedYear: "2023",
    services: ["Building Automation", "Energy Management", "Smart Controls", "Security Integration"]
  },
  {
    id: 6,
    company: "Apollo Healthcare Group",
    location: "Bangalore, Karnataka",
    contactPerson: "Dr. Rajesh Kumar",
    designation: "Chief Operations Officer",
    testimonial: "The precision and reliability required for healthcare facilities demand the highest standards of MEP engineering. JS Consultants exceeded our expectations with their specialized healthcare systems design, ensuring 100% uptime for critical operations and maintaining the strictest safety and hygiene standards.",
    duration: "4+ years partnership",
    category: "Healthcare",
    projectType: "Hospital MEP Systems",
    projectValue: "$3.2M",
    rating: 5,
    logo: "/images/testimonials/client-2.jpg",
    completedYear: "2023",
    services: ["Medical Gas Systems", "Critical Power", "HVAC with Filtration", "Fire Safety"]
  }
]

export default function TestimonialsContent() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/testimonials/client-1.jpg"
            alt="Satisfied Clients and Professional Testimonials"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <ScrollReveal>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center text-white"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="inline-flex items-center px-4 py-2 bg-blue-600/20 text-blue-300 rounded-full text-sm font-medium backdrop-blur-sm border border-blue-400/30 mb-6"
              >
                <Award className="w-4 h-4 mr-2" />
                Client Success Stories
              </motion.div>

              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Client <span className="text-blue-400">Testimonials</span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto">
                Discover what our clients say about our 15+ years of MEP engineering excellence and commitment to delivering exceptional results
              </p>

              {/* Client Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-8 max-w-4xl mx-auto"
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">98%</div>
                  <div className="text-sm text-gray-300">Client Satisfaction</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">500+</div>
                  <div className="text-sm text-gray-300">Happy Clients</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">15+</div>
                  <div className="text-sm text-gray-300">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">100%</div>
                  <div className="text-sm text-gray-300">Project Success</div>
                </div>
              </motion.div>
            </motion.div>
          </ScrollReveal>
        </div>
      </section>

      {/* Featured Testimonials Carousel */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                What Our Clients Say
              </h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Real feedback from satisfied clients across various industries
              </p>
            </div>
          </ScrollReveal>
          
          <TestimonialCarousel testimonials={testimonials} />
        </div>
      </section>

      {/* Detailed Testimonials Grid */}
      <section className="py-20 bg-gradient-to-b from-slate-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <ScrollReveal>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Detailed Client Success Stories
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                In-depth testimonials showcasing our commitment to engineering excellence and client satisfaction
              </p>
            </div>
          </ScrollReveal>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <ScrollReveal key={testimonial.id} delay={index * 0.1}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300"
                >
                  {/* Client Image */}
                  <div className="relative h-48">
                    <Image
                      src={testimonial.logo}
                      alt={testimonial.company}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>

                    {/* Rating */}
                    <div className="absolute top-4 right-4">
                      <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                    </div>

                    {/* Project Value */}
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-green-600/90 text-white rounded-full text-sm font-medium">
                        {testimonial.projectValue}
                      </span>
                    </div>

                    {/* Company Info Overlay */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <h3 className="text-xl font-bold text-white">{testimonial.company}</h3>
                      <p className="text-blue-300 text-sm">{testimonial.category}</p>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    {/* Quote */}
                    <div className="mb-6">
                      <Quote className="w-8 h-8 text-blue-600 mb-3" />
                      <p className="text-gray-700 leading-relaxed italic">
                        "{testimonial.testimonial}"
                      </p>
                    </div>

                    {/* Client Details */}
                    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Building className="w-4 h-4 text-blue-600" />
                          <span className="text-gray-600">{testimonial.contactPerson}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4 text-green-600" />
                          <span className="text-gray-600">{testimonial.location}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-purple-600" />
                          <span className="text-gray-600">{testimonial.completedYear}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Award className="w-4 h-4 text-orange-600" />
                          <span className="text-gray-600">{testimonial.duration}</span>
                        </div>
                      </div>
                    </div>

                    {/* Services */}
                    <div className="mb-4">
                      <h4 className="font-semibold text-gray-900 mb-2">Services Provided</h4>
                      <div className="flex flex-wrap gap-2">
                        {testimonial.services.map((service, idx) => (
                          <span key={idx} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                            {service}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Project Type */}
                    <div className="pt-4 border-t border-gray-100">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Project Type:</span>
                        <span className="text-sm font-semibold text-gray-900">{testimonial.projectType}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-slate-900 to-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <ScrollReveal>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-white"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Join Our Satisfied Clients?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Experience the same quality, innovation, and commitment that has earned us these outstanding testimonials and long-term partnerships
              </p>

              {/* Trust Indicators */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8 max-w-3xl mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">98%</div>
                  <div className="text-sm text-gray-400">Client Retention</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">15+</div>
                  <div className="text-sm text-gray-400">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">500+</div>
                  <div className="text-sm text-gray-400">Projects Delivered</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">24/7</div>
                  <div className="text-sm text-gray-400">Support Available</div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-700 transition-colors shadow-lg"
                >
                  Request Free Consultation
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-white/30 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:border-blue-400 hover:text-blue-400 transition-colors"
                >
                  View Our Portfolio
                </motion.button>
              </div>
            </motion.div>
          </ScrollReveal>
        </div>
      </section>

      <Footer />
    </div>
  )
}
