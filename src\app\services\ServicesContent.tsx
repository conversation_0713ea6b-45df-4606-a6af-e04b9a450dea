"use client";

import React, { useState } from 'react';
import { motion } from 'motion/react';
import { useInView } from 'react-intersection-observer';
import Header from '@/components/header/Header';
import Footer from '@/components/footer/Footer';
import { Zap, Wind, Shield, Droplets, Sun, Building, ArrowRight, ChevronDown, Award, CheckCircle } from 'lucide-react';
import Image from 'next/image';

const ServicesContent = () => {
  const [heroRef, heroInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [servicesRef, servicesInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [expandedService, setExpandedService] = useState<number | null>(null);

  const services = [
    {
      id: 1,
      title: "Electrical Engineering",
      icon: Zap,
      color: "from-blue-500 to-blue-600",
      image: "/images/services/electrical.jpg",
      description: "Complete electrical design and installation services for residential, commercial, and industrial projects with focus on safety, efficiency, and code compliance.",
      features: [
        "Power Distribution Systems",
        "Lighting Design & Control",
        "Emergency Power Systems",
        "Electrical Safety & Compliance",
        "Energy Management Systems",
        "Motor Control Centers"
      ],
      capabilities: [
        "Load calculations and power system analysis",
        "Electrical panel design and specification",
        "Cable routing and conduit systems",
        "Grounding and bonding systems",
        "Electrical code compliance and permits",
        "Testing and commissioning"
      ],
      applications: ["Commercial Buildings", "Industrial Facilities", "Residential Complexes", "Healthcare Facilities", "Educational Institutions"]
    },
    {
      id: 2,
      title: "HVAC Systems",
      icon: Wind,
      color: "from-green-500 to-green-600",
      image: "/images/services/hvac.jpg",
      description: "Advanced heating, ventilation, and air conditioning solutions for optimal comfort, energy efficiency, and indoor air quality management.",
      features: [
        "Climate Control Systems",
        "Air Quality Management",
        "Energy Efficient Design",
        "Ventilation Systems",
        "Ductwork Design",
        "Smart Controls"
      ],
      capabilities: [
        "Load calculations and system sizing",
        "Equipment selection and specification",
        "Ductwork design and layout",
        "Control system integration",
        "Energy modeling and optimization",
        "Maintenance planning"
      ],
      applications: ["Office Buildings", "Retail Spaces", "Manufacturing Plants", "Hospitals", "Data Centers"]
    },
    {
      id: 3,
      title: "Fire Protection Systems",
      icon: Shield,
      color: "from-red-500 to-red-600",
      image: "/images/services/mechanical.jpg",
      description: "Comprehensive fire safety and protection systems designed to safeguard your property, personnel, and comply with all safety regulations.",
      features: [
        "Fire Detection Systems",
        "Sprinkler Systems",
        "Fire Suppression",
        "Emergency Evacuation",
        "Smoke Management",
        "Fire Alarm Systems"
      ],
      capabilities: [
        "Fire risk assessment and analysis",
        "System design and hydraulic calculations",
        "Code compliance and approvals",
        "Installation supervision",
        "Testing and commissioning",
        "Maintenance programs"
      ],
      applications: ["High-rise Buildings", "Industrial Facilities", "Warehouses", "Shopping Centers", "Hotels"]
    },
    {
      id: 4,
      title: "Plumbing Engineering",
      icon: Droplets,
      color: "from-cyan-500 to-cyan-600",
      image: "/images/services/plumbing.jpg",
      description: "Professional plumbing engineering services including water supply, drainage, and specialized piping systems for all building types.",
      features: [
        "Water Supply Systems",
        "Drainage & Sewerage",
        "Hot Water Systems",
        "Rainwater Harvesting",
        "Water Treatment",
        "Fixture Installation"
      ],
      capabilities: [
        "Hydraulic design and calculations",
        "Pipe sizing and material selection",
        "Pump selection and installation",
        "Water quality management",
        "Leak detection and repair",
        "System optimization"
      ],
      applications: ["Residential Buildings", "Commercial Complexes", "Industrial Plants", "Hospitals", "Schools"]
    },
    {
      id: 5,
      title: "Solar Power Systems",
      icon: Sun,
      color: "from-yellow-500 to-yellow-600",
      image: "/images/services/electrical.jpg",
      description: "Sustainable solar energy solutions designed to reduce operational costs and environmental impact while ensuring reliable power generation.",
      features: [
        "Solar Panel Installation",
        "Grid-tie Systems",
        "Battery Storage",
        "Energy Monitoring",
        "Net Metering",
        "Maintenance Services"
      ],
      capabilities: [
        "Solar resource assessment",
        "System design and modeling",
        "Financial analysis and ROI",
        "Grid interconnection",
        "Performance monitoring",
        "Maintenance and support"
      ],
      applications: ["Rooftop Solar", "Ground Mount Systems", "Commercial Solar", "Industrial Solar", "Solar Farms"]
    },
    {
      id: 6,
      title: "Building Automation (IBMS)",
      icon: Building,
      color: "from-purple-500 to-purple-600",
      image: "/images/services/hvac.jpg",
      description: "Intelligent Building Management Systems for automated control, monitoring, and optimization of all building systems and operations.",
      features: [
        "Building Automation",
        "Energy Management",
        "Security Integration",
        "Environmental Monitoring",
        "Predictive Maintenance",
        "Data Analytics"
      ],
      capabilities: [
        "System integration and programming",
        "Sensor network design",
        "User interface development",
        "Data collection and analysis",
        "Remote monitoring setup",
        "Performance optimization"
      ],
      applications: ["Smart Buildings", "Corporate Offices", "Hospitals", "Universities", "Manufacturing Facilities"]
    }
  ];

  const toggleService = (serviceId: number) => {
    setExpandedService(expandedService === serviceId ? null : serviceId);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section ref={heroRef} className="relative py-20 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/services/electrical.jpg"
            alt="Professional MEP Engineering Services"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={heroInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 bg-blue-600/20 text-blue-300 rounded-full text-sm font-medium backdrop-blur-sm border border-blue-400/30 mb-4"
            >
              <Award className="w-4 h-4 mr-2" />
              Comprehensive MEP Engineering Services
            </motion.div>

            <h1 className="text-4xl lg:text-6xl font-bold text-white">
              Our <span className="text-blue-400">Engineering Services</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Comprehensive electrical and MEP engineering solutions tailored to meet your project requirements with precision, innovation, and unwavering commitment to excellence.
            </p>

            {/* Service Categories */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-3 gap-4 pt-8 max-w-4xl mx-auto"
            >
              {services.slice(0, 6).map((service, index) => (
                <div key={service.id} className="flex items-center space-x-2 text-gray-300 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                  <span>{service.title}</span>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section ref={servicesRef} className="py-20 bg-gradient-to-b from-slate-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={servicesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Professional Engineering Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From concept to completion, we deliver integrated MEP engineering services that exceed industry standards and client expectations.
            </p>
          </motion.div>

          <div className="space-y-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              const isExpanded = expandedService === service.id;
              
              return (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={servicesInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300"
                >
                  {/* Service Header */}
                  <div 
                    className="p-8 cursor-pointer"
                    onClick={() => toggleService(service.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-6">
                        <div className="relative">
                          <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${service.color} flex items-center justify-center`}>
                            <IconComponent className="w-8 h-8 text-white" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-2xl font-bold text-gray-900">{service.title}</h3>
                          <p className="text-gray-600 mt-2 max-w-2xl">{service.description}</p>
                        </div>
                      </div>
                      <motion.div
                        animate={{ rotate: isExpanded ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ChevronDown className="w-6 h-6 text-gray-400" />
                      </motion.div>
                    </div>
                  </div>

                  {/* Expanded Content */}
                  <motion.div
                    initial={false}
                    animate={{ height: isExpanded ? "auto" : 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-8 pb-8 border-t border-gray-100">
                      {/* Service Image */}
                      <div className="relative h-48 rounded-xl overflow-hidden mt-6 mb-8">
                        <Image
                          src={service.image}
                          alt={service.title}
                          fill
                          className="object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                      </div>

                      <div className="grid md:grid-cols-3 gap-8">
                        {/* Features */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                            Key Features
                          </h4>
                          <ul className="space-y-2">
                            {service.features.map((feature, idx) => (
                              <li key={idx} className="text-gray-600 text-sm flex items-center">
                                <ArrowRight className="w-3 h-3 mr-2 text-gray-400" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Capabilities */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                            Our Capabilities
                          </h4>
                          <ul className="space-y-2">
                            {service.capabilities.map((capability, idx) => (
                              <li key={idx} className="text-gray-600 text-sm flex items-center">
                                <ArrowRight className="w-3 h-3 mr-2 text-gray-400" />
                                {capability}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Applications */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                            Applications
                          </h4>
                          <div className="space-y-2">
                            {service.applications.map((application, idx) => (
                              <div key={idx} className="inline-block bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full mr-2 mb-2">
                                {application}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* CTA */}
                      <div className="mt-8 pt-6 border-t border-gray-100">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
                        >
                          Get Quote for {service.title}
                          <ArrowRight className="ml-2 w-4 h-4" />
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-slate-900 to-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={servicesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-white">
              Ready to Start Your Project?
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Contact us today to discuss your requirements and get a customized MEP engineering solution for your project.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
              >
                Contact Us Today
                <ArrowRight className="ml-2 w-5 h-5" />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-lg hover:border-blue-400 hover:text-blue-400 transition-colors"
              >
                View Our Projects
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ServicesContent;
