'use client'

import { motion } from 'motion/react'
import { Linkedin, Mail, Award, Calendar } from 'lucide-react'

interface TeamMember {
  id: number
  name: string
  position: string
  department: string
  experience: string
  specializations: string[]
  bio: string
  image?: string
  linkedin?: string
  email?: string
  achievements?: string[]
}

interface TeamProfileProps {
  member: TeamMember
  delay?: number
}

export function TeamProfile({ member, delay = 0 }: TeamProfileProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
      whileHover={{ y: -5, scale: 1.02 }}
      className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
    >
      {/* Profile Image */}
      <div className="relative h-64 bg-gradient-to-br from-blue-500 to-slate-600">
        {member.image ? (
          <img 
            src={member.image} 
            alt={member.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <span className="text-3xl font-bold text-white">
                {member.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
          </div>
        )}
        
        {/* Social Links Overlay */}
        <div className="absolute top-4 right-4 flex space-x-2">
          {member.linkedin && (
            <a 
              href={member.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
            >
              <Linkedin className="w-4 h-4 text-white" />
            </a>
          )}
          {member.email && (
            <a 
              href={`mailto:${member.email}`}
              className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
            >
              <Mail className="w-4 h-4 text-white" />
            </a>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-4">
          <h3 className="text-xl font-bold text-slate-800 mb-1">
            {member.name}
          </h3>
          <p className="text-blue-600 font-semibold mb-1">
            {member.position}
          </p>
          <p className="text-slate-600 text-sm">
            {member.department}
          </p>
        </div>

        {/* Experience */}
        <div className="flex items-center text-slate-600 text-sm mb-4">
          <Calendar className="w-4 h-4 mr-2" />
          {member.experience}
        </div>

        {/* Bio */}
        <p className="text-slate-700 text-sm leading-relaxed mb-4">
          {member.bio}
        </p>

        {/* Specializations */}
        <div className="mb-4">
          <h4 className="font-semibold text-slate-800 text-sm mb-2">Specializations:</h4>
          <div className="flex flex-wrap gap-2">
            {member.specializations.map((spec, index) => (
              <span 
                key={index}
                className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium"
              >
                {spec}
              </span>
            ))}
          </div>
        </div>

        {/* Achievements */}
        {member.achievements && member.achievements.length > 0 && (
          <div>
            <h4 className="font-semibold text-slate-800 text-sm mb-2 flex items-center">
              <Award className="w-4 h-4 mr-1" />
              Key Achievements:
            </h4>
            <ul className="space-y-1">
              {member.achievements.slice(0, 2).map((achievement, index) => (
                <li key={index} className="text-slate-600 text-xs flex items-start">
                  <span className="w-1 h-1 bg-blue-600 rounded-full mt-1.5 mr-2 flex-shrink-0"></span>
                  {achievement}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </motion.div>
  )
}
