import React from "react";
import { motion } from "motion/react";
import { div } from "motion/react-client";

type Props = {};

const CallToActionSection = (props: Props) => {
  return (
    <div className="px-10 py-5">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mt-16"
      >
        <div className="bg-gradient-to-r from-blue-400 to-violet-800 rounded-2xl p-8 lg:p-12">
          <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
            Experience the JS Consultants Difference
          </h3>
          <p className="text-gray-50 mb-8 max-w-2xl mx-auto">
            Partner with a team that combines technical expertise, innovative
            solutions, and unwavering commitment to your project's success.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition-all duration-300"
            >
              Schedule Consultation
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-lg hover:border-blue-400 hover:text-blue-400 transition-all duration-300"
            >
              Learn About Our Team
            </motion.button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default CallToActionSection;
