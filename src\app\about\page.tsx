import React from 'react';
import { Metadata } from 'next';
import AboutContent from './AboutContent';
import { useInView } from 'react-intersection-observer';
import { Award, Lightbulb, Target, Users } from 'lucide-react';
import Header from '@/components/header/Header';

export const metadata: Metadata = {
  title: 'About Us - JS Consultants | Electrical & MEP Engineering Experts',
  description: 'Learn about JS Consultants, a specialized electrical and MEP engineering firm with 15+ years of experience. Meet our leadership team and discover our mission.',
  keywords: 'electrical engineering, MEP engineering, about JS Consultants, engineering team, Chennai engineers',
  openGraph: {
    title: 'About JS Consultants - Leading Electrical & MEP Engineering Firm',
    description: 'Discover the story behind JS Consultants, our experienced leadership team, and our commitment to engineering excellence.',
    type: 'website',
    locale: 'en_IN',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About JS Consultants - Engineering Excellence',
    description: 'Meet the team behind JS Consultants and learn about our journey in electrical and MEP engineering.',
  }
};

export default function AboutPage() {
  return <AboutContent />;
}
